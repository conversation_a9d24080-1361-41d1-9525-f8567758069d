import { CustomBaseEntity } from "../../common/entities";
import { Column, Entity, Index, JoinColumn, ManyToOne, OneToMany, OneToOne } from "typeorm";
import { Upload } from "../../upload/entities/upload.entity";
// import { LicensePlate } from "./license-plate.entity";
import { Order } from "../../order/entities/order.entity";
import { NamePrefix } from "../../name-prefix/entities/name-prefix.entity";
import { CustomerBank } from "./customer-bank.entity";
import { Invoice } from "src/invoice/entities/invoice.entity";
import { Sale } from "../../sale/entities/sale.entity";

@Entity()
export class Customer extends CustomBaseEntity {
    @Column()
    @Index({ unique: true })
    code: string;

    @Column()
    name: string;

    @Column({ nullable: true })
    address: string;

    @Column({ nullable: true })
    tax: string;

    @Column({ nullable: true })
    company: string;

    @Column('simple-array', { nullable: true })
    licensePlates: string[];

    @Column({ name: 'phone_number', nullable: true })
    phoneNumber: string;

    @Column({ name: 'point_balance', nullable: true, default: 0 })
    pointBalance: number;

    // @ManyToOne(() => Level, (_) => _.customers)
    // @JoinColumn({ name: 'level_id' })
    // level: Level;

    @ManyToOne(() => Upload, (_) => _.customers)
    @JoinColumn({ name: 'identity_card_id' })
    identityCard: Upload;

    // @OneToMany(() => LicensePlate, (_) => _.customer)
    // licensePlates: LicensePlate[];

    @OneToMany(() => CustomerBank, (_) => _.customer)
    customerBanks: CustomerBank[];

    @OneToMany(() => Order, (_) => _.customer)
    orders: Order[];

    @OneToMany(() => Invoice, (_) => _.customer)
    invoices: Invoice[];

    @OneToMany(() => Sale, (sale) => sale.customer)
    sales: Sale[];

    // @ManyToOne(() => NamePrefix, (_) => _.customers)
    // @JoinColumn({ name: 'prefix_id' })
    // prefix: NamePrefix;

    constructor(partial?: Partial<Customer>) {
        super();
        if (partial) {
            Object.assign(this, partial)
        }
    }
}
