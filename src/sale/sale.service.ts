import { Injectable, NotFoundException, BadRequestException, Inject, forwardRef } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { paginate, PaginateQuery, PaginateConfig, FilterOperator } from 'nestjs-paginate';
import { Sale, SaleStatus } from './entities/sale.entity';
import { SaleItem } from './entities/sale-item.entity';
import { CreateSaleDto } from './dto/create-sale.dto';
import { UpdateSaleDto } from './dto/update-sale.dto';
import { InventoryService } from '../inventory/inventory.service';
import { TransactionType } from '../inventory/entities/inventory-transaction.entity';
import { Product } from 'src/product/entities/product.entity';

export const SALE_PAGINATION_CONFIG: PaginateConfig<Sale> = {
  sortableColumns: ['id', 'saleNumber', 'saleDate', 'status', 'totalAmount', 'subtotal', 'createdAt'],
  defaultSortBy: [['createdAt', 'DESC']],
  searchableColumns: ['saleNumber', 'notes'],
  relations: {
    customer: true,
    branch: true,
    warehouse: true,
    soldBy: true,
    approvedBy: true,
    items: {
      product: true,
    },
    paymentMethod: true,
  },
  filterableColumns: {
    status: true,
    branchId: true,
    warehouseId: true,
    customerId: true,
    saleDate: [FilterOperator.BTW]
  }
};

@Injectable()
export class SaleService {
  constructor(
    @InjectRepository(Sale)
    private readonly saleRepository: Repository<Sale>,
    @InjectRepository(SaleItem)
    private readonly saleItemRepository: Repository<SaleItem>,
    @Inject(forwardRef(() => InventoryService))
    private readonly inventoryService: InventoryService,
    private readonly dataSource: DataSource,
  ) { }

  async datatables(query: PaginateQuery) {
    return paginate(query, this.saleRepository, SALE_PAGINATION_CONFIG);
  }

  async findAll() {
    return this.saleRepository.find({
      relations: {
        customer: true,
        branch: true,
        warehouse: true,
        soldBy: true,
        approvedBy: true,
        items: {
          product: {
            category: true,
            unit: true
          },
        },
        paymentMethod: true
      },
      order: {
        createdAt: 'DESC'
      }
    });
  }

  async findOne(id: number) {
    const sale = await this.saleRepository.findOne({
      where: { id },
      relations: {
        customer: true,
        branch: true,
        warehouse: true,
        soldBy: true,
        approvedBy: true,
        items: {
          product: {
            category: true,
            unit: true
          },
        },
        paymentMethod: true
      }
    });

    if (!sale) {
      throw new NotFoundException(`Sale with ID ${id} not found`);
    }

    return sale;
  }

  async findBySaleNumber(saleNumber: string) {
    const sale = await this.saleRepository.findOne({
      where: { saleNumber },
      relations: {
        customer: true,
        branch: true,
        warehouse: true,
        soldBy: true,
        approvedBy: true,
        items: {
          product: true,
        },
        paymentMethod: true
      }
    });

    if (!sale) {
      throw new NotFoundException(`Sale with Sale Number ${saleNumber} not found`);
    }

    return sale;
  }

  async create(createSaleDto: CreateSaleDto, userId: number) {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // ตรวจสอบว่าเลขที่ Sale ซ้ำหรือไม่
      const existingSale = await queryRunner.manager.findOne(Sale, {
        where: { saleNumber: createSaleDto.saleNumber }
      });

      if (existingSale) {
        throw new BadRequestException(`Sale number ${createSaleDto.saleNumber} already exists`);
      }

      // สร้าง Sale
      const sale = queryRunner.manager.create(Sale, {
        ...createSaleDto,
        saleDate: new Date(createSaleDto.saleDate),
        approvedAt: createSaleDto.approvedAt ? new Date(createSaleDto.approvedAt) : null,
        createdBy: { id: userId } as any,
        soldBy: { id: userId } as any,
        customer: { id: createSaleDto.customerId } as any,
        branch: { id: createSaleDto.branchId } as any,
        warehouse: { id: createSaleDto.warehouseId } as any,
        paymentMethod: { id: createSaleDto.paymentMethodId } as any,
        approvedBy: createSaleDto.approvedById ? { id: createSaleDto.approvedById } as any : null,
      });

      const savedSale = await queryRunner.manager.save(sale);

      // สร้าง SaleItems
      for (const itemDto of createSaleDto.items) {
        const product = await Product.findOne({ where: { id: itemDto.productId }, relations: { unit: true } });

        if (!product) {
          throw new NotFoundException(`Product with ID ${itemDto.productId} not found`);
        }

        const item = queryRunner.manager.create(SaleItem, {
          ...itemDto,
          // expiryDate: itemDto.expiryDate ? new Date(itemDto.expiryDate) : null,
          sale: savedSale,
          product: { id: itemDto.productId } as any,
          productName: product.name,
          productCode: product.code,
          unitName: product.unit.name
        });

        await queryRunner.manager.save(item);
      }

      await queryRunner.commitTransaction();
      return this.findOne(savedSale.id);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async update(id: number, updateSaleDto: UpdateSaleDto, userId: number) {
    const sale = await this.findOne(id);

    if (sale.status === SaleStatus.COMPLETED) {
      throw new BadRequestException('Cannot update completed sale');
    }

    const updateData = this.saleRepository.create({
      ...updateSaleDto,
      customer: { id: updateSaleDto.customerId } as any,
      branch: { id: updateSaleDto.branchId } as any,
      warehouse: { id: updateSaleDto.warehouseId } as any,
      paymentMethod: { id: updateSaleDto.paymentMethodId } as any,
    })

    delete updateData.items;

    await this.saleRepository.update(id, updateData);

    //Update Item
    if (updateSaleDto.items) {
      await this.saleItemRepository.delete({ sale: { id } });

      for (const itemDto of updateSaleDto.items) {
        const item = this.saleItemRepository.create({
          ...itemDto,
          sale: { id } as any,
          product: { id: itemDto.productId } as any
        });

        await this.saleItemRepository.save(item);
      }
    }

    return this.findOne(id);
  }

  async remove(id: number) {
    const sale = await this.findOne(id);

    if (sale.status === SaleStatus.COMPLETED) {
      throw new BadRequestException('Cannot delete completed sale');
    }

    await this.saleRepository.softDelete(id);
  }

  async generateSaleNumber(): Promise<any> {
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');

    const prefix = `SL${year}${month}`;

    // หาเลขที่ล่าสุดในเดือนนี้
    const lastSale = await this.saleRepository
      .createQueryBuilder('sale')
      .where('sale.saleNumber LIKE :prefix', { prefix: `${prefix}%` })
      .orderBy('sale.saleNumber', 'DESC')
      .getOne();

    let nextNumber = 1;
    if (lastSale) {
      const lastNumber = parseInt(lastSale.saleNumber.substring(prefix.length));
      nextNumber = lastNumber + 1;
    }

    return { data: `${prefix}${String(nextNumber).padStart(4, '0')}` };
  }

  async approve(id: number, userId: number) {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const sale = await queryRunner.manager.findOne(Sale, {
        where: { id },
        relations: {
          items: {
            product: true
          },
          warehouse: true,
          customer: true,
          branch: true
        }
      });

      if (!sale) {
        throw new NotFoundException(`Sale with ID ${id} not found`);
      }

      if (sale.status === SaleStatus.COMPLETED) {
        throw new BadRequestException('Sale is already completed');
      }

      // อัปเดตสถานะเป็น COMPLETED และปรับปรุงสินค้าคงคลัง
      await queryRunner.manager.update(Sale, id, {
        status: SaleStatus.COMPLETED,
        approvedBy: { id: userId } as any,
        approvedAt: new Date(),
      });

      // ปรับปรุงสินค้าคงคลังสำหรับแต่ละรายการ (หักออก)
      for (const item of sale.items) {
        if (item.quantity > 0) {
          try {
            // หา inventory
            const inventory = await this.inventoryService.findByProductAndLocation(
              item.product.id,
              sale.branch.id,
              sale.warehouse.id
            );

            if (!inventory) {
              throw new BadRequestException(`Product ${item.productName} not found in inventory`);
            }

            // ตรวจสอบว่ามีสินค้าเพียงพอหรือไม่
            if (inventory.availableQuantity < item.quantity) {
              throw new BadRequestException(`Insufficient inventory for product ${item.productName}. Available: ${inventory.availableQuantity}, Required: ${item.quantity}`);
            }

            // หักสินค้าออกจาก inventory
            await this.inventoryService.createTransaction({
              inventoryId: inventory.id,
              type: TransactionType.SALE,
              quantity: -item.quantity, // ลบออก
              unitCost: item.unitCost || item.unitPrice,
              description: `Sale: ${sale.saleNumber} - ${item.product.name}`,
              referenceType: 'sale',
              referenceId: sale.id,
              referenceNumber: sale.saleNumber,
            }, userId);

          } catch (error) {
            console.error(`Error updating inventory for item ${item.product.name}:`, error);
            throw error; // ต้อง throw error เพื่อ rollback transaction
          }
        }
      }

      await queryRunner.commitTransaction();
      return this.findOne(id);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async changeStatus(id: number, status: SaleStatus) {
    await this.saleRepository.update(id, { status });
  }
}
