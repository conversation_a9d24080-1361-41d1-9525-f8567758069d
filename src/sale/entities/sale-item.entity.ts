import { Column, Entity, ManyToOne } from "typeorm";
import { CustomBaseEntity } from "../../common/entities";
import { DecimalColumnTransformer } from "../../common/decimal-column-transformer";
import { Product } from "../../product/entities/product.entity";
import { Sale } from "./sale.entity";

@Entity()
export class SaleItem extends CustomBaseEntity {
    @ManyToOne(() => Sale, (sale) => sale.items, { onDelete: 'CASCADE' })
    sale: Sale;

    @ManyToOne(() => Product, (product) => product.saleItems)
    product: Product;

    // @Column()
    // productName: string; // ชื่อสินค้า (เก็บไว้เผื่อสินค้าถูกลบ)

    // @Column()
    // productCode: string; // รหัสสินค้า (เก็บไว้เผื่อสินค้าถูกลบ)

    @Column({ type: 'numeric', transformer: new DecimalColumnTransformer() })
    quantity: number; // จำนวนที่ขาย

    @Column({ type: 'numeric', transformer: new DecimalColumnTransformer() })
    unitPrice: number; // ราคาต่อหน่วย

    @Column({ nullable: true })
    unitName: string; // ชื่อหน่วย

    @Column({ type: 'numeric', transformer: new DecimalColumnTransformer() })
    totalPrice: number; // ราคารวม

    @Column({ type: 'numeric', transformer: new DecimalColumnTransformer(), nullable: true })
    unitCost: number; // ต้นทุนต่อหน่วย (สำหรับคำนวณกำไร)

    @Column({ type: 'text', nullable: true })
    notes: string; // หมายเหตุ

    // @Column({ nullable: true })
    // batchNumber: string; // หมายเลข lot/batch

    // @Column({ nullable: true })
    // expiryDate: Date; // วันหมดอายุ

    constructor(partial?: Partial<SaleItem>) {
        super();
        if (partial) {
            Object.assign(this, partial);
        }
    }
}
