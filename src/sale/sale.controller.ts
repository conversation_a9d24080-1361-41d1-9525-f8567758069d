import { 
  Controller, 
  Get, 
  Post, 
  Body, 
  Put, 
  Param, 
  Delete, 
  HttpCode, 
  HttpStatus, 
  ParseIntPipe, 
  Req,
  Query,
  Patch
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery } from '@nestjs/swagger';
import { Request } from 'express';
import { ApiPaginationQuery, Paginate, PaginateQuery } from 'nestjs-paginate';
import { Auth } from '../auth/decorators/auth.decorator';
import { SaleService, SALE_PAGINATION_CONFIG } from './sale.service';
import { CreateSaleDto } from './dto/create-sale.dto';
import { UpdateSaleDto } from './dto/update-sale.dto';
import { SaleStatus } from './entities/sale.entity';

@Controller('sale')
@ApiTags('ใบขาย (Sale)')
@Auth()
export class SaleController {
  constructor(private readonly saleService: SaleService) {}

  @Get('datatables')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'ดึงข้อมูลใบขายแบบ pagination' })
  @ApiPaginationQuery(SALE_PAGINATION_CONFIG)
  @ApiResponse({ status: 200, description: 'ดึงข้อมูลสำเร็จ' })
  datatables(@Paginate() query: PaginateQuery) {
    return this.saleService.datatables(query);
  }

  @Get('generate-sale-number')
  @ApiOperation({ summary: 'สร้างเลขที่ใบขายอัตโนมัติ' })
  @ApiResponse({ status: 200, description: 'สร้างเลขที่สำเร็จ' })
  generateSaleNumber() {
    return this.saleService.generateSaleNumber();
  }

  @Post()
  @ApiOperation({ summary: 'สร้างใบขายใหม่' })
  @ApiResponse({ status: 201, description: 'สร้างใบขายสำเร็จ' })
  @ApiResponse({ status: 400, description: 'ข้อมูลไม่ถูกต้อง' })
  create(@Req() req: Request, @Body() createSaleDto: CreateSaleDto) {
    const userId = req.user['sub'];
    return this.saleService.create(createSaleDto, userId);
  }

  @Get()
  @ApiOperation({ summary: 'ดึงข้อมูลใบขายทั้งหมด' })
  @ApiResponse({ status: 200, description: 'ดึงข้อมูลสำเร็จ' })
  findAll() {
    return this.saleService.findAll();
  }

  @Get('by-sale-number/:saleNumber')
  @ApiOperation({ summary: 'ดึงข้อมูลใบขายตามเลขที่ Sale' })
  @ApiParam({ name: 'saleNumber', description: 'เลขที่ใบขาย' })
  @ApiResponse({ status: 200, description: 'ดึงข้อมูลสำเร็จ' })
  @ApiResponse({ status: 404, description: 'ไม่พบใบขาย' })
  findBySaleNumber(@Param('saleNumber') saleNumber: string) {
    return this.saleService.findBySaleNumber(saleNumber);
  }

  @Get(':id')
  @ApiOperation({ summary: 'ดึงข้อมูลใบขายตาม ID' })
  @ApiParam({ name: 'id', description: 'รหัสใบขาย' })
  @ApiResponse({ status: 200, description: 'ดึงข้อมูลสำเร็จ' })
  @ApiResponse({ status: 404, description: 'ไม่พบใบขาย' })
  findOne(@Param('id', ParseIntPipe) id: number) {
    return this.saleService.findOne(id);
  }

  @Put(':id')
  @ApiOperation({ summary: 'อัปเดตใบขาย' })
  @ApiParam({ name: 'id', description: 'รหัสใบขาย' })
  @ApiResponse({ status: 200, description: 'อัปเดตสำเร็จ' })
  @ApiResponse({ status: 400, description: 'ข้อมูลไม่ถูกต้อง' })
  @ApiResponse({ status: 404, description: 'ไม่พบใบขาย' })
  update(
    @Param('id', ParseIntPipe) id: number, 
    @Body() updateSaleDto: UpdateSaleDto,
    @Req() req: Request
  ) {
    const userId = req.user['sub'];
    return this.saleService.update(id, updateSaleDto, userId);
  }

  @Patch(':id/status')
  @ApiOperation({ summary: 'เปลี่ยนสถานะใบขาย' })
  @ApiParam({ name: 'id', description: 'รหัสใบขาย' })
  @ApiQuery({ name: 'status', enum: SaleStatus, description: 'สถานะใหม่' })
  @ApiResponse({ status: 200, description: 'เปลี่ยนสถานะสำเร็จ' })
  @ApiResponse({ status: 400, description: 'ไม่สามารถเปลี่ยนสถานะได้' })
  @ApiResponse({ status: 404, description: 'ไม่พบใบขาย' })
  changeStatus(
    @Param('id', ParseIntPipe) id: number, 
    @Query('status') status: SaleStatus
  ) {
    return this.saleService.changeStatus(id, status);
  }

  @Patch(':id/approve')
  @ApiOperation({ summary: 'อนุมัติใบขายและปรับปรุงสินค้าคงคลัง' })
  @ApiParam({ name: 'id', description: 'รหัสใบขาย' })
  @ApiResponse({ status: 200, description: 'อนุมัติสำเร็จ' })
  @ApiResponse({ status: 400, description: 'ไม่สามารถอนุมัติได้' })
  @ApiResponse({ status: 404, description: 'ไม่พบใบขาย' })
  approve(@Param('id', ParseIntPipe) id: number, @Req() req: Request) {
    const userId = req.user['sub'];
    return this.saleService.approve(id, userId);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'ลบใบขาย' })
  @ApiParam({ name: 'id', description: 'รหัสใบขาย' })
  @ApiResponse({ status: 200, description: 'ลบสำเร็จ' })
  @ApiResponse({ status: 400, description: 'ไม่สามารถลบได้' })
  @ApiResponse({ status: 404, description: 'ไม่พบใบขาย' })
  remove(@Param('id', ParseIntPipe) id: number) {
    return this.saleService.remove(id);
  }
}
