import { 
    Entity, 
    Column, 
    ManyToOne, 
    OneToMany, 
    Index 
} from 'typeorm';
import { CustomBaseEntity } from '../../common/entities/custom-base.entity';
import { DecimalColumnTransformer } from '../../common/decimal-column-transformer';
import { Customer } from '../../customer/entities/customer.entity';
import { Branch } from '../../branch/entities/branch.entity';
import { Warehouse } from '../../warehouse/entities/warehouse.entity';
import { User } from '../../user/entities/user.entity';
import { QuotationItem } from './quotation-item.entity';

export enum QuotationStatus {
    DRAFT = 'draft',
    SENT = 'sent',
    ACCEPTED = 'accepted',
    REJECTED = 'rejected',
    EXPIRED = 'expired',
    CONVERTED = 'converted' // แปลงเป็นใบขายแล้ว
}

@Entity()
export class Quotation extends CustomBaseEntity {
    @Index()
    @Column({ unique: true })
    quotationNumber: string; // เลขที่ใบเสนอราคา

    @Column()
    quotationDate: Date; // วันที่เสนอราคา

    @Column({ nullable: true })
    validUntil: Date; // วันที่หมดอายุของใบเสนอราคา

    @Column({ type: 'enum', enum: QuotationStatus, default: QuotationStatus.DRAFT })
    status: QuotationStatus;

    @ManyToOne(() => Customer, (customer) => customer.quotations)
    customer: Customer;

    @ManyToOne(() => Branch, (branch) => branch.quotations)
    branch: Branch;

    @ManyToOne(() => Warehouse, (warehouse) => warehouse.quotations)
    warehouse: Warehouse;

    @Column({ type: 'boolean', default: false })
    isVat: boolean;

    @Column({ type: 'numeric', transformer: new DecimalColumnTransformer(), default: 0 })
    taxRate: number;

    @Column({ type: 'numeric', transformer: new DecimalColumnTransformer(), default: 0 })
    taxAmount: number;
    
    @Column({ type: 'numeric', transformer: new DecimalColumnTransformer(), default: 0 })
    subtotal: number; // ยอดรวม

    @Column({ type: 'numeric', transformer: new DecimalColumnTransformer(), default: 0 })
    discount: number; // ส่วนลด

    @Column({ type: 'numeric', transformer: new DecimalColumnTransformer(), default: 0 })
    totalAmount: number; // ยอดรวมสุทธิ

    @Column({ type: 'text', nullable: true })
    notes: string; // หมายเหตุ

    @Column({ type: 'text', nullable: true })
    terms: string; // เงื่อนไขการชำระเงิน

    @ManyToOne(() => User, (user) => user.quotations)
    createdBy: User; // ผู้สร้างใบเสนอราคา

    @ManyToOne(() => User, { nullable: true })
    approvedBy: User; // ผู้อนุมัติ

    @Column({ nullable: true })
    approvedAt: Date; // วันที่อนุมัติ

    @OneToMany(() => QuotationItem, (item) => item.quotation, { cascade: true })
    items: QuotationItem[];

    // Reference to converted sale (if any)
    @Column({ nullable: true })
    convertedSaleId: number; // ID ของใบขายที่แปลงมาจากใบเสนอราคานี้

    @Column({ nullable: true })
    convertedAt: Date; // วันที่แปลงเป็นใบขาย
}
