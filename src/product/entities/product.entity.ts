import { Category } from "../../category/entities/category.entity";
import { CustomBaseEntity } from "../../common/entities";
import { Column, Entity, Index, JoinColumn, ManyToOne, OneToMany } from "typeorm";
import { OrderItem } from "../../order/entities/order-item.entity";
import { DecimalColumnTransformer } from "../../common/decimal-column-transformer";
import { Unit } from "../../unit/entities/unit.entity";
import { Upload } from "../../upload/entities/upload.entity";
import { InvoiceItem } from "../../invoice/entities/invoice-item.entity";
import { Branch } from "../../branch/entities/branch.entity";
import { QueueItem } from "../../queue/entities/queue-item.entity";
import { Type } from "../dto/create-product.dto";
import { PurchaseOrderItem } from "../../purchase-order/entities/purchase-order-item.entity";
import { Inventory } from "../../inventory/entities/inventory.entity";
import { GoodsReceiptItem } from "src/goods-receipt/entities";
import { SaleItem } from "../../sale/entities/sale-item.entity";

@Entity()
export class Product extends CustomBaseEntity {
    @Column()
    @Index({ unique: true })
    code: string;

    @Column()
    name: string;

    @Column({ type: 'enum', enum: Type, nullable: true })
    type: Type

    @ManyToOne(() => Upload)
    @JoinColumn({ name: 'image_id' })
    image: Upload;

    @Column({ name: 'color_code', nullable: true })
    colorCode: string;

    @Column('numeric', {default: 0, comment: 'ราคาซื้อ', transformer: new DecimalColumnTransformer() })
    price: number;

    @Column('numeric', {default: 0, comment: 'ราคาขาย', transformer: new DecimalColumnTransformer() })
    sellingPrice: number;

    @ManyToOne(() => Category, (_) => _.products)
    @JoinColumn({ name: 'category_id' })
    category: Category;

    @OneToMany(() => OrderItem, (_) => _.product)
    orderItems: OrderItem[];

    @OneToMany(() => InvoiceItem, (_) => _.product)
    invoiceItems: InvoiceItem[];

    // @OneToMany(() => ProductAttribute, (_) => _.product)
    // productAttributes: ProductAttribute[];

    @ManyToOne(() => Unit, (_) => _.products)
    @JoinColumn({ name: 'unit_id' })
    unit: Unit;

    // @OneToMany(() => ProductLevel, (_) => _.product)
    // productLevels: ProductLevel[];

    @ManyToOne(() => Branch, (_) => _.products)
    @JoinColumn({ name: 'branch_id' })
    branch: Branch;

    @OneToMany(() => QueueItem, (_) => _.product)
    queueItems: QueueItem[];

    @OneToMany(() => PurchaseOrderItem, (_) => _.product)
    purchaseOrderItems: PurchaseOrderItem[];

    @OneToMany(() => Inventory, (_) => _.product)
    inventories: Inventory[];

    @OneToMany(() => GoodsReceiptItem, (_) => _.product)
    goodsReceiptItems: GoodsReceiptItem[];

    @OneToMany(() => SaleItem, (saleItem) => saleItem.product)
    saleItems: SaleItem[];

    // Computed properties for inventory
    // get totalStock(): number {
    //     // if (!this.inventories) return 0;
    //     // return this.inventories.reduce((total, inv) => total + inv.currentStock, 0);

    //     return this.goodsReceiptItems.reduce((total, item) => total + item.receivedQuantity, 0);
    // }

    constructor(partial?: Partial<Product>) {
        super();
        if (partial) {
            Object.assign(this, partial)
        }
    }
}
